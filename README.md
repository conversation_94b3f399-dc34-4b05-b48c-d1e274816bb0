# Log Generator

Tool tự động tạo file log dựa vào cấu hình YAML với hỗ trợ VIN data generation.

## Tính năng

- Đ<PERSON><PERSON> cấu hình từ file YAML
- Sinh timestamp tuần tự với increments nhỏ để mô phỏng log thực tế
- Thay thế các placeholder (UUID, timestamp, VIN, Internal VIN, API ID, ...) bằng giá trị từ CSV hoặc ngẫu nhiên
- Sinh file log theo format và tạo tên file theo pattern
- Hỗ trợ nén file log bằng gzip (tùy chọn)
- Ước tính số dòng log cần thiết để đạt kích thước file target
- Tạo CSV chứa VIN data với range tùy chỉnh
- Multi-threading để tăng tốc độ generation
- CLI interface để điều khiển quá trình sinh log

## Cấu trúc thư mục

```
log_generator/
  ├── config.yaml           # Cấu hình chính
  ├── Makefile              # Build automation (Linux/macOS)
  ├── run.py                # Python script tương đương Makefile (Windows)
  ├── src/                  # Mã nguồn
  │   ├── config_loader.py      # Module đọc và phân tích cấu hình
  │   ├── log_generator.py      # Module sinh log với VIN support
  │   ├── log_writer.py         # Module ghi và nén file
  │   ├── vin_csv_generator.py  # Module tạo CSV chứa VIN data
  │   └── cli.py                # Command-line interface
  ├── output/               # Thư mục chứa file output
  │   ├── csv/                  # CSV files (vehicle_region.csv)
  │   └── st/                   # Log files theo cấu trúc foundation
  └── README.md             # Tài liệu này
```

## Cài đặt

```bash
# Cài đặt các package cần thiết
pip install pyyaml tqdm
```

## Sử dụng

### Linux/macOS (sử dụng Makefile)

```bash
# Cài đặt dependencies
make setup

# Tạo CSV với VIN data (theo range trong config)
make csv

# Chạy tất cả foundations
make all

# Chạy foundation cụ thể
make run-foundation FOUNDATION=apf-dsn-flow
make run-foundation FOUNDATION=apf-msg-flow

# Dọn dẹp output
make clean
```

### Windows (sử dụng Python script hoặc Batch)

**Option 1: Python script**
```cmd
# Cài đặt dependencies
python run.py setup

# Tạo CSV với VIN data
python run.py csv

# Chạy tất cả foundations
python run.py all

# Chạy foundation cụ thể
python run.py foundation apf-dsn-flow
python run.py foundation apf-msg-flow

# Dọn dẹp output
python run.py clean
```

**Option 2: Batch script (đơn giản hơn)**
```cmd
# Cài đặt dependencies
run.bat setup

# Tạo CSV với VIN data
run.bat csv

# Chạy tất cả foundations
run.bat all

# Chạy foundation cụ thể
run.bat foundation apf-dsn-flow
run.bat foundation apf-msg-flow

# Dọn dẹp output
run.bat clean
```

### Sử dụng CLI trực tiếp

```bash
# Tạo CSV với số lượng records tùy chỉnh
python -m src.vin_csv_generator --config config.yaml --output output/csv/vehicle_region.csv --count 100000

# Sinh log cho application cụ thể
python -m src.cli --config config.yaml --output-dir output --app-name apf-dsn-flow --threads 8

# Chạy thử để xem thông tin mà không tạo file
python -m src.cli --config config.yaml --dry-run
```

## Cấu hình

File `config.yaml` có cấu trúc như sau:

```yaml
logs:
  - file_size_after_compression: 100 MB  # Target file size

timestamp:
  from: 2025-05-10 00:00:00        # Thời gian bắt đầu
  to: 2025-05-17 23:59:00          # Thời gian kết thúc
  format: "%Y-%m-%d %H:%M:%S,%f"   # Định dạng timestamp

internal_vin:                      # Cấu hình VIN data
  internal_vin_start: 9999         # Internal VIN bắt đầu
  internal_vin_end: 999999         # Internal VIN kết thúc
  columns:                         # Cấu trúc CSV
    InternalVin: __INTERNAL_VIN__  # Integer type
    Vin: VIN__INTERNAL_VIN__       # String: VIN + số (không có leading zeros)
    RegisteredCountry: [10]        # Random từ list
    PfDistinction: [18,20,24]      # Random từ list
    CvServiceId: "NULL"            # Literal NULL string
    EndDateTime: "NULL"            # Literal NULL string

foundation:
  apf-dsn-flow:                    # Foundation name
    file_count: 1                  # Số file tạo
    format: >-                     # Log format với placeholders
      __TIMESTAMP__ I apf-dsn-flow api-msg 79 24 [__UUID__][DataSendRule#102] [__API_ID__:__PF__:* __API_NAME__] __BODY__
    path: st/base/logs/online/api-msg  # Output path
    file_name: apf-dsn-flow_apf-dsn-ext-01_%y%m%d%H%M%S  # File name pattern
    file_extension: .log           # File extension
    log_line_use_rate: 1           # Log rate
    variables:                     # Variables
      __API_ID__: ["130_2_99", "160_2_99"]
      __PF__: [18, 20, 24]
      __API_NAME__: ["api1", "api2"]
      __BODY__:                    # Body có thể chứa __VIN__ và __INTERNAL_VIN__
        - 'run str data={"Vin":"__VIN__","AppRequestNo":"...","internalVin": __INTERNAL_VIN__}'
        - 'communicate str data={"Vin":"__VIN__","internalVin":__INTERNAL_VIN__}'
```

## Các placeholder

Tool sẽ tự động thay thế các placeholder sau trong format log:

- `__TIMESTAMP__`: Timestamp tuần tự với increments nhỏ (mô phỏng log thực tế)
- `__UUID__`: UUID random cho mỗi dòng log
- `__VIN__`: VIN từ CSV (format: VIN + số, không có leading zeros)
- `__INTERNAL_VIN__`: Internal VIN từ CSV (integer type, không có leading zeros)
- `__BODY__`: Random selection từ list body templates
- Các biến khác được định nghĩa trong phần `variables`

## VIN Data Generation

Tool tự động tạo CSV chứa VIN data với:

- **InternalVin**: Integer từ `internal_vin_start` đến `internal_vin_end`
- **Vin**: String format `VIN{InternalVin}` (không có leading zeros)
- **RegisteredCountry**: Random từ list config
- **PfDistinction**: Random từ list config
- **CvServiceId**: Literal "NULL" string
- **EndDateTime**: Literal "NULL" string

Ví dụ CSV output:
```csv
InternalVin,Vin,RegisteredCountry,PfDistinction,CvServiceId,EndDateTime
9999,VIN9999,10,20,NULL,NULL
10000,VIN10000,10,18,NULL,NULL
```

## Performance

- **Multi-threading**: Sử dụng multiple threads để tăng tốc generation
- **Memory efficient**: Stream processing cho file lớn
- **Progress tracking**: Real-time progress với tqdm
- **File size estimation**: Thuật toán ước tính chính xác để đạt target size

## Troubleshooting

### Windows Issues

Nếu gặp lỗi trên Windows:

```cmd
# Đảm bảo Python path đúng
set PYTHONPATH=%CD%
python run.py all

# Hoặc sử dụng trực tiếp
python -m src.cli --config config.yaml --output-dir output --threads 8
```

### Memory Issues

Với file CSV lớn (>1M records):

```bash
# Giảm số threads
python run.py all --threads 4

# Hoặc chạy từng foundation
python run.py foundation apf-dsn-flow
python run.py foundation apf-msg-flow
```

### File Size Issues

Nếu file output quá lớn/nhỏ:

- Điều chỉnh `file_size_after_compression` trong config.yaml
- Tool sẽ tự động ước tính số dòng cần thiết

## Lưu ý

- File log có thể được nén gzip (tùy chọn trong config)
- Kích thước file được ước tính dựa trên sample để đạt target size
- VIN và Internal VIN được load từ CSV để đảm bảo consistency
- Timestamp sử dụng sequential generation với increments nhỏ để mô phỏng log thực tế
- CSV sẽ được tạo tự động nếu chưa tồn tại
- Range VIN được định nghĩa trong `internal_vin_start` và `internal_vin_end`
