#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Configuration loader for log generator.
This module reads and parses the YAML configuration file.
"""

import yaml
import os
import csv
import pandas as pd
from typing import Dict, Any, List, Union


class ConfigLoader:
    """
    YAML configuration loader for log generator.
    """

    def __init__(self, config_path: str):
        """
        Initialize config loader with config file path.
        
        Args:
            config_path: Path to the YAML configuration file
        """
        self.config_path = config_path
        self.config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """
        Load configuration from YAML file.
        
        Returns:
            Dict containing the configuration
        """
        if not os.path.exists(self.config_path):
            raise FileNotFoundError(f"設定ファイルが見つかりません: {self.config_path}")
            
        with open(self.config_path, 'r') as file:
            try:
                return yaml.safe_load(file)
            except yaml.YAMLError as e:
                raise ValueError(f"YAMLファイルの解析エラー: {e}")
    
    def get_log_config(self) -> Dict[str, Any]:
        """
        Get log configuration.
        
        Returns:
            Dict containing log configuration
        """
        return self.config.get('logs', [])
    
    def get_timestamp_config(self) -> Dict[str, str]:
        """
        Get timestamp configuration.
        
        Returns:
            Dict containing timestamp configuration
        """
        return self.config.get('timestamp', {})
    
    def get_foundation_config(self) -> Dict[str, Any]:
        """
        Get foundation configuration.
        
        Returns:
            Dict containing foundation configuration with app names as keys
        """
        foundation = self.config.get('foundation', {})
        # Ensure foundation is a dict and not None
        if foundation is None:
            return {}
        # If foundation is already a dict, return it directly
        if isinstance(foundation, dict):
            return foundation
            
        # If we reach here, it's an unexpected type
        print(f"Warning: Unexpected foundation type: {type(foundation)}. Expected dict.")
        return {}
    
    def get_log_format(self, app_name: str) -> str:
        """
        Get log format string for a specific application.
        
        Args:
            app_name: Name of the application
            
        Returns:
            Log format string
        """
        foundation = self.get_foundation_config()
        app_config = foundation.get(app_name, {})
        
        if not app_config:
            return ""
            
        # Trả về trực tiếp giá trị format từ cấu hình ứng dụng
        return app_config.get('format', '')
    
    def get_log_path(self, app_name: str) -> str:
        """
        Get log path for a specific application.
        
        Args:
            app_name: Name of the application
            
        Returns:
            Log path
        """
        foundation = self.get_foundation_config()
        app_config = foundation.get(app_name, {})
        
        if not app_config:
            return "logs"  # Default log path
            
        return app_config.get('path', 'logs')  # Default log path
    
    def get_file_name_pattern(self, app_name: str) -> str:
        """
        Get file name pattern for a specific application.
        
        Args:
            app_name: Name of the application
            
        Returns:
            File name pattern
        """
        foundation = self.get_foundation_config()
        app_config = foundation.get(app_name, {})
        
        if not app_config:
            return ""
            
        return app_config.get('file_name', '')
    
    def get_file_extension(self, app_name: str) -> str:
        """
        Get file extension for a specific application.
        
        Args:
            app_name: Name of the application
            
        Returns:
            File extension
        """
        foundation = self.get_foundation_config()
        app_config = foundation.get(app_name, {})
        
        if not app_config:
            return ".log"  # Default extension
            
        return app_config.get('file_extension', '.log')  # Default extension
    
    def get_log_line_use_rate(self, app_name: str) -> float:
        """
        Get log line use rate for a specific application.
        
        Args:
            app_name: Name of the application
            
        Returns:
            Log line use rate
        """
        foundation = self.get_foundation_config()
        app_config = foundation.get(app_name, {})
        
        if not app_config:
            return 1.0
            
        return float(app_config.get('log_line_use_rate', 1.0))
    
    def get_variables(self, app_name: str) -> dict:
        """
        Get variables for a specific application.
        
        Args:
            app_name: Name of the application
            
        Returns:
            Dict containing variables and their values
        """
        foundation = self.get_foundation_config()
        app_config = foundation.get(app_name, {})
        
        if not app_config:
            return {}
            
        # Lấy toàn bộ variables từ cấu hình ứng dụng
        variables = app_config.get('variables', {})
        
        # Nếu variables là list, chuyển đổi thành dict
        if isinstance(variables, list):
            return {var['key']: var['value'] for var in variables if 'key' in var and 'value' in var}
            
        return variables if isinstance(variables, dict) else {}
    
    def get_file_count(self, app_name: str = None) -> int:
        """
        Get number of log files to generate for a specific application.
        
        Args:
            app_name: Name of the application. If None, returns the global file count.
            
        Returns:
            Number of log files to generate
        """
        # If app_name is provided, try to get file_count from the app's config
        if app_name:
            foundation = self.get_foundation_config()
            app_config = foundation.get(app_name, {})
            if 'file_count' in app_config:
                return int(app_config['file_count'])
        
        # Fall back to global file_count in logs section
        logs = self.get_log_config()
        for log in logs:
            if isinstance(log, dict) and 'file_count' in log:
                return int(log['file_count'])
                
        return 1  # Default to 1 if not specified
    
    def get_file_size_after_compression(self) -> str:
        """
        Get target file size after compression.
        
        Returns:
            Target file size after compression
        """
        logs = self.get_log_config()
        for log in logs:
            if 'file_size_after_compression' in log:
                return log['file_size_after_compression']
        return "0"  # Default to 0 if not specified
        
    def get_vin_config(self) -> Dict[str, Any]:
        """
        Get VIN (Vehicle Identification Number) configuration.

        Returns:
            Dict containing VIN configuration with keys:
            - char_count: Number of characters in the VIN
            - prefix: Prefix for VIN generation
            - vin_start: Starting number for VIN sequence
            - vin_end: Ending number for VIN sequence
            - count: Total count of VINs to generate
            - values: List of internal_vin values for random selection
        """
        return self.config.get('internal_vin', {})

    def get_internal_vin_range(self) -> tuple:
        """
        Get internal VIN range for generation.

        Returns:
            Tuple of (start, end) for internal VIN range
        """
        vin_config = self.get_vin_config()
        start = int(vin_config.get('internal_vin_start', 1))
        end = int(vin_config.get('internal_vin_end', 9999999))
        return (start, end)

    def get_vehicle_region_csv_path(self) -> str:
        """
        Get path to vehicle region CSV file.

        Returns:
            Path to vehicle_region.csv file
        """
        return "output/csv/vehicle_region.csv"

    def ensure_vehicle_region_csv(self) -> None:
        """
        Ensure vehicle_region.csv exists, create if not.
        """
        csv_path = self.get_vehicle_region_csv_path()

        # Create directory if not exists
        os.makedirs(os.path.dirname(csv_path), exist_ok=True)

        if not os.path.exists(csv_path):
            print(f"Creating vehicle region CSV at {csv_path}")
            self._generate_vehicle_region_csv(csv_path, max_records=100000)
        else:
            print(f"Vehicle region CSV already exists at {csv_path}")

    def _generate_vehicle_region_csv(self, csv_path: str, max_records: int = 100000) -> None:
        """
        Generate vehicle_region.csv file with internal VIN data.

        Args:
            csv_path: Path to create CSV file
            max_records: Maximum number of records to generate (default: 100000)
        """
        vin_config = self.get_vin_config()
        start, end = self.get_internal_vin_range()

        # Limit records to max_records for practical purposes
        total_possible = end - start + 1
        actual_records = min(max_records, total_possible)

        # Get columns configuration
        columns_config = vin_config.get('columns', {})

        print(f"Generating vehicle region CSV with {actual_records:,} records (from range {start:,}-{end:,})...")

        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            # Use columns from config as header
            fieldnames = list(columns_config.keys())
            writer = csv.writer(csvfile)

            # Write header
            writer.writerow(fieldnames)

            # Generate records
            for i in range(actual_records):
                if (i + 1) % 10000 == 0:
                    print(f"Generated {i + 1:,} records...")

                # Generate sequential internal VIN starting from start
                vin_number = start + i
                internal_vin_str = f"{vin_number:07d}"  # 7-digit string for VIN

                row = []
                for column_name in fieldnames:
                    column_config = columns_config[column_name]
                    if column_config == "__INTERNAL_VIN__":
                        row.append(vin_number)  # Use integer, no leading zeros
                    elif column_config == "VIN__INTERNAL_VIN__":
                        row.append(f"VIN{internal_vin_str}")  # VIN still uses 7-digit format
                    elif column_config == "NULL" or column_config is None:
                        row.append("NULL")  # Use literal "NULL" string
                    elif isinstance(column_config, list):
                        # Random selection from list
                        import random
                        row.append(random.choice(column_config))
                    else:
                        row.append(column_config)

                writer.writerow(row)

        print(f"Successfully generated vehicle region CSV with {actual_records:,} records")

    def load_internal_vin_values(self) -> List[str]:
        """
        Load internal VIN values from vehicle_region.csv.

        Returns:
            List of internal VIN values
        """
        csv_path = self.get_vehicle_region_csv_path()

        if not os.path.exists(csv_path):
            raise FileNotFoundError(f"Vehicle region CSV not found at {csv_path}")

        internal_vins = []
        with open(csv_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                # Look for InternalVin column
                if 'InternalVin' in row:
                    internal_vins.append(row['InternalVin'])

        print(f"Loaded {len(internal_vins):,} internal VIN values from CSV")
        return internal_vins


if __name__ == "__main__":
    # テスト用コード
    config = ConfigLoader("../config.yaml")
    print("Timestamp config:", config.get_timestamp_config())
    print("Log format:", config.get_log_format("apf-dsn-flow"))
    print("Variables:", config.get_variables("apf-dsn-flow"))
