#!/usr/bin/env python3
"""
Windows-compatible runner script equivalent to Makefile.
Provides same functionality as Makefile for cross-platform compatibility.
"""

import os
import sys
import subprocess
import shutil
import argparse
from pathlib import Path


def run_command(cmd, cwd=None, check=True):
    """Run a command and handle errors."""
    print(f"Running: {cmd}")
    if cwd:
        print(f"Working directory: {cwd}")
    
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            cwd=cwd, 
            check=check,
            text=True,
            capture_output=False
        )
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {e}")
        return False


def setup():
    """Install required dependencies."""
    print("Installing dependencies...")
    # Try python3 first, then python
    if shutil.which("python3"):
        return run_command("python3 -m pip install pyyaml tqdm")
    else:
        return run_command("python -m pip install pyyaml tqdm")


def clean():
    """Clean output directories."""
    print("Cleaning output directories...")
    
    paths_to_clean = [
        "output/st",
        "output/csv"
    ]
    
    for path in paths_to_clean:
        if os.path.exists(path):
            print(f"Removing {path}")
            shutil.rmtree(path)
    
    print("Clean completed.")
    return True


def create_csv():
    """Generate VIN CSV file."""
    print("Generating VIN CSV file...")

    # Ensure output directory exists
    os.makedirs("output/csv", exist_ok=True)

    # Try python3 first, then python
    python_cmd = "python3" if shutil.which("python3") else "python"
    cmd = f"{python_cmd} -m src.vin_csv_generator --config config.yaml --output output/csv/vehicle_region.csv"
    env = os.environ.copy()
    env["PYTHONPATH"] = os.getcwd()

    try:
        result = subprocess.run(
            cmd,
            shell=True,
            cwd=os.getcwd(),
            check=True,
            text=True,
            env=env
        )
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"Error generating CSV: {e}")
        return False


def run_foundation(foundation_name):
    """Run specific foundation."""
    print(f"Running foundation: {foundation_name}")

    python_cmd = "python3" if shutil.which("python3") else "python"
    cmd = f"{python_cmd} -m src.cli --config config.yaml --output-dir output --app-name {foundation_name} --threads 8"
    env = os.environ.copy()
    env["PYTHONPATH"] = os.getcwd()

    try:
        result = subprocess.run(
            cmd,
            shell=True,
            cwd=os.getcwd(),
            check=True,
            text=True,
            env=env
        )
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"Error running foundation {foundation_name}: {e}")
        return False


def run_all():
    """Run all foundations."""
    print("Starting log generation for all foundations...")

    python_cmd = "python3" if shutil.which("python3") else "python"
    cmd = f"{python_cmd} -m src.cli --config config.yaml --output-dir output --threads 8"
    env = os.environ.copy()
    env["PYTHONPATH"] = os.getcwd()

    try:
        result = subprocess.run(
            cmd,
            shell=True,
            cwd=os.getcwd(),
            check=True,
            text=True,
            env=env
        )
        print("Done.")
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"Error running all foundations: {e}")
        return False


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Log Generator Runner - Windows compatible version of Makefile",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run.py setup              # Install dependencies
  python run.py csv                # Generate VIN CSV
  python run.py all                # Run all foundations
  python run.py foundation apf-dsn-flow  # Run specific foundation
  python run.py clean              # Clean output directories
        """
    )
    
    parser.add_argument(
        "command",
        choices=["setup", "csv", "all", "foundation", "clean"],
        help="Command to execute"
    )
    
    parser.add_argument(
        "foundation_name",
        nargs="?",
        help="Foundation name (required for 'foundation' command)"
    )
    
    args = parser.parse_args()
    
    # Change to script directory
    script_dir = Path(__file__).parent.absolute()
    os.chdir(script_dir)
    
    success = False
    
    if args.command == "setup":
        success = setup()
    elif args.command == "clean":
        success = clean()
    elif args.command == "csv":
        success = create_csv()
    elif args.command == "all":
        success = run_all()
    elif args.command == "foundation":
        if not args.foundation_name:
            print("Error: foundation name is required for 'foundation' command")
            print("Available foundations: apf-dsn-flow, apf-msg-flow")
            sys.exit(1)
        success = run_foundation(args.foundation_name)
    
    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main()
