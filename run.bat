@echo off
REM Windows Batch Script for Log Generator
REM Equivalent to Makefile for Windows users

setlocal enabledelayedexpansion

if "%1"=="" (
    echo Usage: run.bat [command] [options]
    echo.
    echo Available commands:
    echo   setup              - Install dependencies
    echo   csv                - Generate VIN CSV file
    echo   all                - Run all foundations
    echo   foundation [name]  - Run specific foundation
    echo   clean              - Clean output directories
    echo.
    echo Examples:
    echo   run.bat setup
    echo   run.bat csv
    echo   run.bat all
    echo   run.bat foundation apf-dsn-flow
    echo   run.bat clean
    goto :eof
)

REM Set PYTHONPATH to current directory
set PYTHONPATH=%CD%

if "%1"=="setup" (
    echo Installing dependencies...
    python -m pip install pyyaml tqdm
    goto :eof
)

if "%1"=="clean" (
    echo Cleaning output directories...
    if exist "output\st" rmdir /s /q "output\st"
    if exist "output\csv" rmdir /s /q "output\csv"
    echo Clean completed.
    goto :eof
)

if "%1"=="csv" (
    echo Generating VIN CSV file...
    if not exist "output\csv" mkdir "output\csv"
    python -m src.vin_csv_generator --config config.yaml --output output\csv\vehicle_region.csv
    goto :eof
)

if "%1"=="all" (
    echo Starting log generation for all foundations...
    python -m src.cli --config config.yaml --output-dir output --threads 8
    echo Done.
    goto :eof
)

if "%1"=="foundation" (
    if "%2"=="" (
        echo Error: Foundation name is required
        echo Available foundations: apf-dsn-flow, apf-msg-flow
        goto :eof
    )
    echo Running foundation: %2
    python -m src.cli --config config.yaml --output-dir output --app-name %2 --threads 8
    goto :eof
)

echo Unknown command: %1
echo Use "run.bat" without arguments to see usage.
